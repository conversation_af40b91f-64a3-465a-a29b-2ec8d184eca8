import { useEffect, useState } from "react";
import { useFetchState } from "@hooks/useFetchState";
import { useTranslation } from "react-i18next";

import { createOrder } from "@modules/wfeOrderFacade/apis/v0";
import { EWFEOrderFacadeUseCase } from "@modules/wfeOrderFacade/constants";
import { TCreateOrderPayload } from "@modules/wfeOrderFacade/interfaces/payloads/ICreateOrderPayload";
import { useSnackBar } from "@common";
import { getErrorToDisplay } from "itsf-ui-common";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import {
    getUniqueService,
} from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { removeAddonActionApiRoute } from "@api-routes/addon";
import { useChannel } from "@common";
import { fetcher } from "@utils/fetcher";
import { createOrderVisit, assigmentComplete } from "@modules/orderStatusFacade/apis/v0";
import { TCreateOrderPayload as OrderStatusPayload, IAssignmentCompleteRequestDto, ServiceDto } from "@modules/orderStatusFacade/interfaces/payloads";
import { EOrderStatusFacadeActivity, EOrderStatusFacadeBusinessStatus, EOrderStatusFacadeCompletionCode, EOrderStatusFacadeUseCase, EOrderStatusFacadeWOStatus } from "@modules/orderStatusFacade/interfaces/constants";

export const useAddOnsCancellation = () => {
    const { startFetching, endFetching  } = useFetchState();
    const { setSnackBarError } = useSnackBar();
    const { t } = useTranslation(["customer", "common"]);
    const [orderIdForSchedule, setOrderIdForSchedule] = useState<string>("");
    const [addedAddons, setAddedAddons] = useState<ICollectionAddOns[]>([]);
    const [isCancellingAddons, setIsCancellingAddons] = useState<boolean>(false);
    const { channel } = useChannel();

    const localStorageKey = localStorage.getItem("USER_SELECTED_CHANNEL");
    const channelLocalStorage = localStorageKey ? JSON.parse(localStorageKey) : null;
    const channelCode = channelLocalStorage?.code ?? channel?.code;
    
    const removeAddOn = (addonsId: number, serviceId: number, cycleDate: string) => {
        return fetcher(removeAddonActionApiRoute(addonsId, cycleDate, channelCode, serviceId), {
            method: "DELETE",
        });
    };


    const handleAddClick = (addon: ICollectionAddOns) => {
        setAddedAddons((prev) => {

            const existingAddon = prev.find((item) => item.id === addon.id);

            // Evitar duplicados
            if (existingAddon) {
                //console.log("Addon already exists in cart:", addon.description);
                return prev;
            }

            const newState = [...prev, addon];
            //console.log("Adding new addon. New state:", newState.map(a => ({id: a.id, desc: a.description})));
            return newState;
        });
    };

    const handleRemoveClick = (addonId: number) => {
        setAddedAddons((prev) => {
            const newState = prev.filter((item) => item.id !== addonId);
            //console.log("Removing addon. New state:", newState.map(a => ({id: a.id, desc: a.description})));
            return newState;
        });
    };

    type ServicesPayload = Array<{
        serviceId: number;
        addOn: { id: number; addonRequestId: number }[];
    }>;

    const callWfeFacadeCancelationAddon = async ({
        contactDetails,
        deliveryInStore,
        setOrderIdForSchedule,
    }: {
        contactDetails: TContactDetails | undefined;
        deliveryInStore?: boolean;
        setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    }): Promise<void> => {

        // Activar estado de carga al inicio
        setIsCancellingAddons(true);

        let orderIdReference: string | undefined;

        // Guards de entrada
        if (!contactDetails || !Array.isArray(contactDetails.emails) || contactDetails.emails.length === 0) {
            setSnackBarError(t("common:noRequesterEmailFound"));
            setIsCancellingAddons(false);
            return;
        }

        // Asegurando que addedAddons existe y sea arreglo
        if (!Array.isArray(addedAddons) || addedAddons.length === 0) {
            setSnackBarError(t("customer:noAddonsToCancel"));
            setIsCancellingAddons(false);
            return;
        }

        try {
            // Obtener orderId
            const { reference } = await getUniqueService();
            orderIdReference = reference;

            // Agrupar por serviceId
            const addonsByServiceGroup = agruparYOrdenarPorServiceId(addedAddons);
            //console.log("addonsByServiceGroup", addonsByServiceGroup);

            // Por cada grupo, cancelar addons y recolectar ids
            const servicesPayload: ServicesPayload = [];

            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);

            for (const grupo of addonsByServiceGroup) {
                if (!grupo || typeof grupo.serviceId !== "number" || !Array.isArray(grupo.items)) {
                    throw new Error(`${t("common:invalidGroupDetected")} (serviceId: ${String(grupo?.serviceId)})`);
                }

                // Ejecutar en paralelo dentro del grupo 
                const addOnArr = await Promise.all(
                    grupo.items.map(async (item: { id: number; description?: string }) => {
                        try {
                            startFetching();
                            //const id_remove_addon_request = await removeAddOn(item.id, grupo.serviceId, yesterday.toISOString());
                            const id_remove_addon_request = await mockRemoveAddOn();

                            if (id_remove_addon_request == null) {
                                throw new Error(t("common:nullAddonRequestId"));
                            }

                            return { id: item.id, addonRequestId: id_remove_addon_request };
                        } catch (e: any) {
                            // Añade contexto del item/grupo
                            const msg = e?.message ?? String(e);
                            throw new Error(
                                `Fallo al obtener id_remove_addon_request para addon ${item.id} del servicio ${grupo.serviceId}: ${msg}`
                            );
                        }finally {
                            endFetching();
                        }
                    })
                );

                servicesPayload.push({
                    serviceId: grupo.serviceId,
                    addOn: addOnArr,
                });
            }

            const appointmentNeeded = appointmentNeededQuestion(addedAddons);
            // Construir payload
            const payload: TCreateOrderPayload = {
                requestorUsername: contactDetails.emails[0].email,
                useCase: EWFEOrderFacadeUseCase.REMOVE_ADDONS,
                appointmentConfirmation: deliveryInStore ? false : appointmentNeeded,
                orderId: orderIdReference,
                services: servicesPayload,
            };

            //console.log("Payload generado:", JSON.stringify(payload, null, 2));

            // return createOrder(payload);


            try {
                startFetching();
                const response = await createOrder(payload, true);
                
                //setOrderIdForSchedule(orderIdReference); 
                setOrderIdForSchedule(response.orderId);


            } catch (error) {
                setSnackBarError(getErrorToDisplay(error));
            } finally {
                endFetching();
            }



        } catch (error: any) {
            console.error(error);
            const message = typeof error?.message === "string" ? error.message : t("customer:addonCancellationError");
            setSnackBarError(message);
        } finally {
            // Desactivar estado de carga al final
            setIsCancellingAddons(false);
        }
    };



    const agruparYOrdenarPorServiceId = (items: ICollectionAddOns[]) => {
        const agrupado = items.reduce((acc, item) => {
            const { serviceId } = item;
            if (!acc[serviceId]) {
            acc[serviceId] = [];
            }
            acc[serviceId].push(item);
            return acc;
        }, {} as Record<number, ICollectionAddOns[]>);

        return Object.entries(agrupado)
            .map(([serviceId, items]) => ({
            serviceId: Number(serviceId),
            items
            }))
            .sort((a, b) => a.serviceId - b.serviceId);
    };


    function mockRemoveAddOn(): Promise<number> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(Math.floor(Math.random() * 10000));
            }, 500);
        });
    }



    //Metodo con el fin de verificar si se necesita visita tecnica
    const appointmentNeededQuestion = (addOns: ICollectionAddOns[]): boolean => {
        return addOns.some(addOn => addOn.itemGroupCode === 'MATERIAL');
    };

    // Método para llamar a los servicios de orderStatusFacade
    const callOrderStatusFacade = async ({
        orderId,
        accountId,
        clientId,
        updatedAddons,
    }: {
        orderId: string ;
        accountId: string ;
        clientId: string ;
        updatedAddons: ICollectionAddOns[];
    }): Promise<void> => {
        
        callOrderStatusFacadeCreateOrderVisit({
            orderId: orderId,
            accountId: accountId,
            clientId: clientId,
            deliveryInStore: false,
        });

        callOrderStatusFacadeAssignmentComplete({
            orderId: orderId,
            updatedAddons: updatedAddons,
        });

    };




    const callOrderStatusFacadeCreateOrderVisit = async ({
        orderId,
        accountId,
        clientId,
        deliveryInStore,
    }: {
        orderId: string ;
        accountId: string ;
        clientId: string ;
        deliveryInStore: boolean;
    }): Promise<void> => {
        

        try {
            // Payload para createOrderVisit con valores null/vacíos
            const createOrderPayload: OrderStatusPayload = {
                orderId: orderId,
                visitId: (deliveryInStore) ? "STORE_"+orderId: orderId,
                useCase: EOrderStatusFacadeUseCase.REMOVE_ADDONS,
                accountId: accountId,
                clientId: clientId,
                activity: EOrderStatusFacadeActivity.FRONTEND,
                businessStatus: EOrderStatusFacadeBusinessStatus.REMOVE_ADDONS,
            };

            // Llamar a createOrderVisit
            const createOrderResponse = await createOrderVisit(createOrderPayload);

            
        } catch (error) {
            console.error("Error en callOrderStatusFacade:", error);
            setSnackBarError(getErrorToDisplay(error));
        }
    };


    const callOrderStatusFacadeAssignmentComplete = async ({
        orderId,
        updatedAddons,
    }: {
        orderId: string ;
        updatedAddons: ICollectionAddOns[];
    }): Promise<void> => {
        
        try {
           
            // Payload para assigmentComplete con valores null/vacíos
            const assignmentPayload: IAssignmentCompleteRequestDto = {
                workOrder: {
                    wochannelID: orderId,
                    services: updatedAddons.map((item) => ({
                        serviceExternalRefID: item.includedEquipmentsCodes?.[0]?.serialNumber ?? t("common:notAvailable"),
                        serviceName: item.description,
                        serviceCode: item.serviceId.toString(),
                        completionCode: (item.recoveredEquipment)? EOrderStatusFacadeCompletionCode.COMPLETED: EOrderStatusFacadeCompletionCode.PENDIENTE,
                    })) as unknown as ServiceDto[],
                    wostatus: EOrderStatusFacadeWOStatus.COMPLETED,
                }
            };

            // Llamar a assigmentComplete
            const assignmentResponse = await assigmentComplete(orderId,assignmentPayload);

        } catch (error) {
            console.error("Error en callOrderStatusFacade:", error);
            setSnackBarError(getErrorToDisplay(error));
        }
    };


    useEffect(() => {
        //console.log("addedAddons state updated:", addedAddons.map(a => ({id: a.id, desc: a.description})));
        //console.log("Total addedAddons count:", addedAddons.length);
    }, [addedAddons]);
    


    return {
        orderIdForSchedule,
        setOrderIdForSchedule,
        callWfeFacadeCancelationAddon,
        callOrderStatusFacade,
        handleAddClick,
        handleRemoveClick,
        addedAddons,
        isCancellingAddons,
        callOrderStatusFacadeCreateOrderVisit,
        callOrderStatusFacadeAssignmentComplete,
    };
};

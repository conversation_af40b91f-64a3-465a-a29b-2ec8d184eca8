import { ADDONS_EQUIPMENT, ADDONS_STATUS, ADDONS_TV } from "@constants";
import { Tabs, Grid, Tab, Typography, Paper, Box, Button } from "@mui/material";
import { IAddOns, ICollectionAddOns } from "@services/subscription/accountId/interface/IAddOns";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";
import { Fragment, useState } from "react";
import dayjs from "dayjs";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useAddonStore } from "@features/crm/SubscriptionsAddOns/useAddonStore";
import { Loader } from "@common";
import { useAddOnsCancellation } from "./useAddOnsCancellation";
import CancellationCart from "./CancellationCart";
import AddonsDeliveredStoreCart from "./AddonsDeliveredStoreCart";
import { useStyle } from "../../style";
import { order } from "@translations/es/translation";

interface IPropsMenuAddons {
    names: { name: string; label: string }[];
    accountId: string;
    addOnsServices: IAddOns | undefined;
    contactDetails: TContactDetails | undefined;
    setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    //Parametros para cancel addon
    setShowCancellationAddon: React.Dispatch<React.SetStateAction<boolean>>;
    OrderIdForSchedule: string;
    handleShowSchedule: (show: boolean) => void;
}

const AddonsCancellation = ({
    names,
    accountId,
    addOnsServices,
    contactDetails,
    setOrderIdForSchedule,
    //Parametros para cancel addon
    setShowCancellationAddon,
    OrderIdForSchedule,
    handleShowSchedule,

}: IPropsMenuAddons) => {
    const { t } = useTranslation(["customer", "common"]);
    const [tabValue, setTabValue] = useState(names[0]?.name ?? "");
    const [showDeliveredStoreCart, setShowDeliveredStoreCart] = useState(false);
    const { classes } = useStyle();

    const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
        setTabValue(newValue);
    };

    const refreshAddons = useAddonStore((state) => state.refreshAddons);
    const setRefreshAddons = useAddonStore((state) => state.setRefreshAddons);
    
    const {
        callWfeFacadeCancelationAddon,
        callOrderStatusFacade,
        handleAddClick,
        handleRemoveClick,
        addedAddons,
        isCancellingAddons,
        callOrderStatusFacadeCreateOrderVisit,
        callOrderStatusFacadeAssignmentComplete,
    } = useAddOnsCancellation();

    // Funciones para manejar AddonsDeliveredStoreCart
    const handleShowDeliveredStoreCart = () => {
        setShowDeliveredStoreCart(true);
    };

    const handleConfirmDeliveredEquipment = (updatedAddons: ICollectionAddOns[]) => {
        console.log("Addons actualizados:", updatedAddons);
        //setShowDeliveredStoreCart(false);
        //setShowCancellationAddon(false);
        callOrderStatusFacadeAssignmentComplete({
            orderId: OrderIdForSchedule,
            updatedAddons: updatedAddons,
        });
    };

    const handleCloseFinalConfirmModal = () => {
        setShowDeliveredStoreCart(false);
        setShowCancellationAddon(false);        
    };

    const handleCancelDeliveredStoreCart = () => {
        setShowDeliveredStoreCart(false);
        setShowCancellationAddon(false);
    };

    
    return (
        <div>
            {refreshAddons ? (
                <Grid alignItems="center" container justifyContent="center" style={{ height: "30vh" }}>
                    <Loader />
                </Grid>
            ) : (
                <>
                    <Grid item md={6} xs={12}>
                        <AddonsDeliveredStoreCart
                            addedAddons={addedAddons}
                            isVisible={showDeliveredStoreCart}
                            onConfirm={handleConfirmDeliveredEquipment}
                            onCancel={handleCancelDeliveredStoreCart}
                            onClose={handleCloseFinalConfirmModal}
                        />
                    </Grid>
                    
                    {!showDeliveredStoreCart && (
                    <Grid container direction="row" spacing={3}>
                        
                    <Grid item md={6} xs={12}>
                        <Grid item>
                            <Typography
                                className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}            
                            >
                                {t("customer:addOnsTitle")}
                            </Typography>
                        </Grid>
                        <Tabs
                            aria-label="Add-ons tabs"
                            value={tabValue}
                            variant="fullWidth"
                            onChange={handleTabChange}
                        >
                            {names?.map(({ name, label }) => (
                                <Tab
                                    disabled={label === "INTERNET"}
                                    key={name}
                                    label={label}
                                    sx={{ fontSize: "14px" }}
                                    value={name}
                                />
                            ))}
                        </Tabs>
                        {addOnsServices?.collection.length !== 0 && addOnsServices !== undefined ? (
                            addOnsServices?.collection
                                .filter((val) => {
                                    // Solo elementos con status NO_ONGOING_REQUEST
                                    if (val.status !== ADDONS_STATUS.NO_ONGOING_REQUEST) {
                                        return false;
                                    }

                                    // Filtrado por pestaña
                                    if (tabValue === ADDONS_TV) {
                                        return (
                                            val.itemGroupCode?.includes(ADDONS_TV) ||
                                            val.itemGroupCode?.includes(ADDONS_EQUIPMENT)
                                        );
                                    }
                                    return val.itemGroupCode?.includes(tabValue);
                                })
                                .map((element) => {
                                    const canShowCancelButton = [
                                        ADDONS_STATUS.ACTIVE,
                                        ADDONS_STATUS.ON_GOING_REQUEST,
                                        ADDONS_STATUS.INITIAL,
                                        ADDONS_STATUS.NO_ONGOING_REQUEST,
                                    ].includes(element.status);

                                    const isInCart = addedAddons.some(addon => addon.id === element.id);

                                    return (
                                        <Fragment key={element.id}>
                                            <Grid
                                                container
                                                direction="row"
                                                alignItems="center"
                                                sx={{
                                                    mt: 1,
                                                    mb: 1,
                                                    p: 2,
                                                    border: "1px solid #A0EAFF",
                                                    backgroundColor: isInCart ? "#0086ff" : "transparent"
                                                }}
                                            >
                                                <Grid item xs={4}>
                                                    <Typography variant="subtitle2" fontWeight="bold" sx={{ color: isInCart ? "#ebebeb" : "#565656" }}>
                                                        {element.description}
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }}>
                                                        ID: {element.id}
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={3}>
                                                    <Typography variant="body2" sx={{ color: isInCart ? "#ebebeb" : "#565656" }}>
                                                        {t("customer:activationDate")}
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }}>
                                                        {element.activatedAt ? dayjs(element.activatedAt).format("DD/MM/YYYY") : "-"}
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={3}>
                                                    <Typography variant="body2" color="text.secondary" sx={{ color: isInCart ? "#ebebeb" : "#565656" }}>
                                                        {t("customer:serial")}
                                                    </Typography>

                                                    <Typography variant="body2" sx={{ color: isInCart ? "#ebebeb" : "text.secondary" }}>
                                                        {(() => {
                                                            const isMaterial =
                                                                String(element.itemGroupCode || "").toLowerCase() === "material";

                                                            if (!isMaterial) return "N/A"; 

                                                            const sn = element.includedEquipmentsCodes?.[0]?.serialNumber;
                                                            const snText = sn == null ? "" : String(sn).trim();

                                                            if (!snText) return t("common:notAvailable");

                                                            return snText;
                                                        })()}
                                                    </Typography>
                                                </Grid>
                                                <Grid item xs={2}>
                                                    {canShowCancelButton && (
                                                        <Button
                                                            variant="contained"
                                                            color="primary"
                                                            size="small"
                                                            onClick={() => handleAddClick(element)}
                                                            disabled={isInCart}
                                                           sx={{
                                                                fontSize: "12px",
                                                                ...(isInCart && {
                                                                    backgroundColor: "primary !important", 
                                                                    color: "#fff !important",              
                                                                    opacity: 1,
                                                                })
                                                            }}
                                                        >
                                                            {isInCart ? t("common:added") : t("customer:cancel")}
                                                        </Button>
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </Fragment>
                                    );
                                })
                        ) : (
                            <Box
                                alignItems="center"
                                display="flex"
                                flexDirection="column"
                                justifyContent="center"
                                minHeight="250px"
                            >
                                <Paper
                                    elevation={0}
                                    sx={{
                                        maxWidth: "500px",
                                        width: "100%",
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <InfoOutlinedIcon color="primary" sx={{ fontSize: 45 }} />
                                    <Typography color="text.secondary" fontWeight="bold" mt={1} variant="h2">
                                        {t("customer:noAddOnsAvailable")}
                                    </Typography>
                                </Paper>
                            </Box>
                        )}

                    </Grid>
                    <Grid item md={6} xs={12}>
                        <CancellationCart
                            addedAddons={addedAddons}
                            onRemoveAddon={handleRemoveClick}
                            onConfirmCancellation={async (deliveryInStore: boolean) => {
                                
                                // Esperar el resultado del método
                                await callWfeFacadeCancelationAddon({
                                    deliveryInStore,
                                    contactDetails,
                                    setOrderIdForSchedule,
                                });

                                // Verificar si hay addons con itemGroupCode "MATERIAL"
                                const hasMaterialAddons = addedAddons.some(addon => addon.itemGroupCode === "MATERIAL");
                                
                                // Si hay addons de tipo MATERIAL, llamar al servicio de orderStatusFacade
                                if (hasMaterialAddons) {
                                    await callOrderStatusFacadeCreateOrderVisit({
                                            orderId:   OrderIdForSchedule,
                                            accountId: accountId,
                                            clientId:  contactDetails?.contactUuid?? "",
                                            deliveryInStore: deliveryInStore,
                                        });
                                }

                            }}
                            handleShowSchedule={handleShowSchedule}
                            setRefreshAddons={setRefreshAddons}
                            setShowCancellationAddon={setShowCancellationAddon}
                            OrderIdForSchedule={OrderIdForSchedule}
                            isCancellingAddons={isCancellingAddons}
                            onShowDeliveredStoreCart={handleShowDeliveredStoreCart}
                        />
                    </Grid>
                </Grid>
                    )}
                </>
            )}
        </div>
    );
};

export default AddonsCancellation;

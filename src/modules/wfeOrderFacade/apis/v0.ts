import { getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { TCreateOrderPayload } from "../interfaces/payloads";
import { IWfeOrderFacadeCreateOrderResponse } from "../interfaces/responses/IWFEOrderFacadeCreateOrderResponse";

const WFE_ENDPOINT = "wfe-order-facade/";
const WFE_PRIVATE_ENDPOINT = getMicroserviceEndpoint(WFE_ENDPOINT, InternalV1EndpointOptions);
const WFE_PRIVATE_ENDPOINT_TEST = "http://127.0.0.1:13419/";

export const cancelOrder = (orderId: string, options: IFetchOptions = {}) =>
    fetcher<void>(`${WFE_PRIVATE_ENDPOINT_TEST}orders/${orderId}`, {
        method: "DELETE",
        ...options,
    });

export const createOrder = (payload: TCreateOrderPayload, isSync = true, options: IFetchOptions = {}) => {
    const url = new URL(`${WFE_PRIVATE_ENDPOINT_TEST}orders`);
    url.searchParams.append("isSync", String(isSync));

    return fetcher<IWfeOrderFacadeCreateOrderResponse>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};
